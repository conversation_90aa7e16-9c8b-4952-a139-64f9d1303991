
package com.dc.summer.model.impl.data;

import com.dc.summer.model.exec.*;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.summer.model.data.DBDDisplayFormat;
import com.dc.summer.model.data.DBDValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

/**
 * Proxy value handler
 */
public class ProxyValue<PERSON>andler implements DBDValueHandler {

    protected final DBDValueHandler target;

    public ProxyValueHandler(DBDValueHandler target) {
        this.target = target;
    }

    @NotNull
    @Override
    public Class<?> getValueObjectType(@NotNull DBSTypedObject attribute) {
        return target.getValueObjectType(attribute);
    }

    @Nullable
    @Override
    public String getValueContentType(@NotNull DBSTypedObject attribute) {
        return target.getValueContentType(attribute);
    }

    @Nullable
    @Override
    public Object fetchValueObject(@NotNull DBCSession session, @NotNull DBCResultSet resultSet, @NotNull DBSTypedObject type, int index) throws DBCException {
        return target.fetchValueObject(session, resultSet, type, index);
    }

    @Override
    public void bindValueObject(@NotNull DBCSession session, @NotNull DBCStatement statement, @NotNull DBSTypedObject type, int index, @Nullable Object value) throws DBCException {
        target.bindValueObject(session, statement, type, index, value);
    }

    @Nullable
    @Override
    public Object getValueFromObject(@NotNull DBCSession session, @NotNull DBSTypedObject type, @Nullable Object object, boolean copy, boolean validateValue) throws DBCException {
        return target.getValueFromObject(session, type, object, copy, false);
    }

    @Override
    public Object createNewValueObject(@NotNull DBCSession session, @NotNull DBSTypedObject type) throws DBCException {
        return target.createNewValueObject(session, type);
    }

    @Override
    public void releaseValueObject(@Nullable Object value) {
        target.releaseValueObject(value);
    }

    @NotNull
    @Override
    public DBCLogicalOperator[] getSupportedOperators(@NotNull DBSTypedObject attribute) {
        return target.getSupportedOperators(attribute);
    }

    @NotNull
    @Override
    public String getValueDisplayString(@NotNull DBSTypedObject column, @Nullable Object value, @NotNull DBDDisplayFormat format) {
        return target.getValueDisplayString(column, value, format);
    }
}