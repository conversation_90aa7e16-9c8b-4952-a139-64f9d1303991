
package com.dc.summer.model;

import com.dc.code.Nullable;

import java.util.Collection;
import java.util.List;

/**
 * Data-source permissions owner
 */
public interface DBPDataSourcePermissionOwner
{
    boolean hasModifyPermission(DBPDataSourcePermission permission);

    List<DBPDataSourcePermission> getModifyPermission();

    void setModifyPermissions(@Nullable Collection<DBPDataSourcePermission> permissions);

}
