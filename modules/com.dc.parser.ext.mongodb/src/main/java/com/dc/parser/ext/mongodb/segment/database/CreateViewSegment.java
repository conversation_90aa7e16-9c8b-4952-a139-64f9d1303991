package com.dc.parser.ext.mongodb.segment.database;

import com.dc.parser.ext.mongodb.segment.BsonArraySegment;
import com.dc.parser.ext.mongodb.segment.BsonObjectSegment;
import com.dc.parser.ext.mongodb.segment.MethodSegment;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateViewSegment extends MethodSegment {
    private String name;
    private String source;
    private BsonArraySegment pipeline;
    private BsonObjectSegment options;
    public CreateViewSegment(int startIndex, int stopIndex) {
        super(startIndex, stopIndex);
    }
}
