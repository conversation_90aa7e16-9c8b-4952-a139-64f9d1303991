
package com.dc.summer.ext.postgresql.model.data.type;

import com.dc.summer.DBException;
import com.dc.summer.ext.postgresql.model.PostgreDataType;
import com.dc.code.NotNull;
import com.dc.code.Nullable;
import com.dc.utils.CommonUtils;

public class PostgreTimeTypeHand<PERSON> extends PostgreTypeHandler {

    public static final PostgreTimeTypeHandler INSTANCE = new PostgreTimeTypeHandler();

    private PostgreTimeTypeHandler() {
        // disallow constructing singleton class
    }

    @Override
    public int getTypeModifiers(@NotNull PostgreDataType type, @NotNull String typeName, @NotNull String[] typmod) throws DBException {
        switch (typmod.length) {
            case 0:
                return EMPTY_MODIFIERS;
            case 1:
                return getTimeModifiers(CommonUtils.toInt(typmod[0]));
            default:
                return super.getTypeModifiers(type, typeName, typmod);
        }
    }

    @NotNull
    @Override
    public String getTypeModifiersString(@NotNull PostgreDataType type, int typmod) {
        final StringBuilder sb = new StringBuilder();
        if (typmod >= 0) {
            final Integer precision = getTypePrecision(type, typmod);
            if (precision != null) {
                sb.append('(').append(precision).append(')');
            }
        }
        return sb.toString();
    }

    @Nullable
    @Override
    public Integer getTypePrecision(@NotNull PostgreDataType type, int typmod) {
        if (typmod < 0) {
            return null;
        }
        return typmod;
    }

    private static int getTimeModifiers(int precision) throws DBException {
        if (precision < 0 || precision > 6) {
            throw new DBException("Time precision " + precision + " must be between 0 and 6");
        }
        return precision;
    }
}
