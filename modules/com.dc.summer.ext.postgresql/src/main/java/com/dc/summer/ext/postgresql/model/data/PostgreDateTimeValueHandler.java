
package com.dc.summer.ext.postgresql.model.data;

import com.dc.summer.model.messages.ModelMessages;
import com.dc.code.NotNull;
import com.dc.summer.ext.postgresql.PostgreConstants;
import com.dc.summer.ext.postgresql.model.PostgreDataSource;
import com.dc.summer.model.data.DBDDataFormatter;
import com.dc.summer.model.data.DBDFormatSettings;
import com.dc.summer.model.exec.DBCException;
import com.dc.summer.model.exec.DBCSession;
import com.dc.summer.model.exec.DBCStatement;
import com.dc.summer.model.exec.jdbc.JDBCPreparedStatement;
import com.dc.summer.model.impl.jdbc.data.handlers.JDBCDateTimeValueHandler;
import com.dc.summer.model.struct.DBSTypedObject;

import java.sql.SQLException;
import java.sql.Timestamp;

/**
 * PostgreDateTimeValueHandler.
 */
public class PostgreDateTimeValueHandler extends JDBCDateTimeValueHandler {
    private static final String POSITIVE_INFINITY_STRING_REPRESENTATION = "infinity";
    private static final String NEGATIVE_INFINITY_STRING_REPRESENTATION = "-infinity";

    // https://jdbc.postgresql.org/documentation/publicapi/constant-values.html
    private static final long NEGATIVE_INFINITY = -9223372036832400000L;
    private static final long NEGATIVE_SMALLER_INFINITY = -185543533774800000L;
    private static final long POSITIVE_INFINITY = 9223372036825200000L;
    private static final long POSITIVE_SMALLER_INFINITY = 185543533774800000L;

    public PostgreDateTimeValueHandler(DBDFormatSettings formatSettings) {
        super(formatSettings);
    }

    @Override
    public Object getValueFromObject(@NotNull DBCSession session, @NotNull DBSTypedObject type, Object object, boolean copy, boolean validateValue) throws DBCException {
        if (!(object instanceof Timestamp)) {
            return super.getValueFromObject(session, type, object, copy, validateValue);
        }
        final long time = ((Timestamp) object).getTime();
        if (time == NEGATIVE_INFINITY || time == NEGATIVE_SMALLER_INFINITY) {
            return NEGATIVE_INFINITY_STRING_REPRESENTATION;
        }
        if (time == POSITIVE_INFINITY || time == POSITIVE_SMALLER_INFINITY) {
            return POSITIVE_INFINITY_STRING_REPRESENTATION;
        }
        return super.getValueFromObject(session, type, object, copy, validateValue);
    }

    @Override
    public void bindValueObject(@NotNull DBCSession session, @NotNull DBCStatement statement, @NotNull DBSTypedObject type, int index, Object value) throws DBCException {
        if (value instanceof String) {
            try {
                ((JDBCPreparedStatement)statement).setObject(index + 1, value.toString(),
                    ((PostgreDataSource)session.getDataSource()).getServerType().getParameterBindType(type, value));
            }
            catch (SQLException e) {
                throw new DBCException(ModelMessages.model_jdbc_exception_could_not_bind_statement_parameter, e);
            }
            return;
        }
        super.bindValueObject(session, statement, type, index, value);
    }

    @NotNull
    @Override
    protected String getFormatterId(DBSTypedObject column) {
        switch (column.getTypeName()) {
            case PostgreConstants.TYPE_TIMETZ:
                return DBDDataFormatter.TYPE_NAME_TIME_TZ;
            case PostgreConstants.TYPE_TIMESTAMPTZ:
                return DBDDataFormatter.TYPE_NAME_TIMESTAMP_TZ;
        }
        return super.getFormatterId(column);
    }
}