package com.dc.parser.exec.engine.segment.dml.order.item;

import com.dc.infra.utils.CaseInsensitiveMap.CaseInsensitiveString;
import com.dc.parser.exec.engine.segment.SegmentType;
import com.dc.parser.exec.engine.segment.dml.expression.ExpressionSegmentBinder;
import com.dc.parser.exec.engine.segment.dml.from.context.TableSegmentBinderContext;
import com.dc.parser.exec.engine.statement.SQLStatementBinderContext;
import com.dc.parser.model.segment.dml.order.item.ExpressionOrderByItemSegment;
import com.google.common.collect.Multimap;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Expression order by item segment binder.
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ExpressionOrderByItemSegmentBinder {

    /**
     * Bind expression order by item segment.
     *
     * @param segment                  expression order by item segment
     * @param binderContext            SQL statement binder context
     * @param tableBinderContexts      table binder contexts
     * @param outerTableBinderContexts outer table binder contexts
     * @param segmentType              segment type
     * @return bound expression order by item segment
     */
    public static ExpressionOrderByItemSegment bind(final ExpressionOrderByItemSegment segment, final SQLStatementBinderContext binderContext,
                                                    final Multimap<CaseInsensitiveString, TableSegmentBinderContext> tableBinderContexts,
                                                    final Multimap<CaseInsensitiveString, TableSegmentBinderContext> outerTableBinderContexts, final SegmentType segmentType) {
        return new ExpressionOrderByItemSegment(segment.getStartIndex(), segment.getStopIndex(), segment.getExpression(), segment.getOrderDirection(), segment.getNullsOrderType().orElse(null),
                ExpressionSegmentBinder.bind(segment.getExpr(), segmentType, binderContext, tableBinderContexts, outerTableBinderContexts));
    }
}
