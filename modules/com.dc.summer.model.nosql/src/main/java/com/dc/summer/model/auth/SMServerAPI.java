package com.dc.summer.model.auth;

public class SMServerAPI {
   private String serverId;
   private String serverVersion;
   private String serverApiVersion;
   private String minimumClientApiVersion;
   private String companyName;

   public String getServerId() {
      return this.serverId;
   }

   public void setServerId(String serverId) {
      this.serverId = serverId;
   }

   public String getServerVersion() {
      return this.serverVersion;
   }

   public void setServerVersion(String serverVersion) {
      this.serverVersion = serverVersion;
   }

   public String getServerApiVersion() {
      return this.serverApiVersion;
   }

   public void setServerApiVersion(String serverApiVersion) {
      this.serverApiVersion = serverApiVersion;
   }

   public String getMinimumClientApiVersion() {
      return this.minimumClientApiVersion;
   }

   public void setMinimumClientApiVersion(String minimumClientApiVersion) {
      this.minimumClientApiVersion = minimumClientApiVersion;
   }

   public String getCompanyName() {
      return this.companyName;
   }

   public void setCompanyName(String companyName) {
      this.companyName = companyName;
   }
}
