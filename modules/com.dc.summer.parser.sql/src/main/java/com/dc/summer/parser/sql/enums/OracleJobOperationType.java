package com.dc.summer.parser.sql.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Oracle 作业操作类型枚举
 * 用于表示对 Oracle 作业的各种操作类型
 */
@Getter
@AllArgsConstructor
public enum OracleJobOperationType {

    /**
     * 创建作业
     */
    CREATE("CREATE"),

    /**
     * 删除作业
     */
    DROP("DROP"),

    /**
     * 运行作业
     */
    RUN("RUN"),

    /**
     * 停止作业
     */
    STOP("STOP"),

    /**
     * 启用作业
     */
    ENABLE("ENABLE"),

    /**
     * 禁用作业
     */
    DISABLE("DISABLE"),

    /**
     * 修改作业
     */
    ALTER("ALTER");

    /**
     * 操作类型的字符串值
     */
    private final String value;

    /**
     * 根据字符串值获取对应的枚举类型
     *
     * @param value 字符串值
     * @return 对应的枚举类型，如果没有找到则返回 null
     */
    public static OracleJobOperationType fromValue(String value) {
        if (value == null) {
            return null;
        }
        for (OracleJobOperationType type : OracleJobOperationType.values()) {
            if (type.getValue().equalsIgnoreCase(value)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据字符串值获取对应的枚举类型名称
     *
     * @param value 字符串值
     * @return 对应的枚举类型名称，如果没有找到则返回空字符串
     */
    public static String getTypeNameByValue(String value) {
        OracleJobOperationType type = fromValue(value);
        return type != null ? type.name() : "";
    }

    @Override
    public String toString() {
        return this.value;
    }
}
