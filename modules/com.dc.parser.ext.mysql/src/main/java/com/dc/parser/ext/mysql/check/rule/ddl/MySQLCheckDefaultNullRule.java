package com.dc.parser.ext.mysql.check.rule.ddl;

import com.dc.parser.ext.mysql.segment.MySQLColumnDefinitionSegment;
import com.dc.parser.ext.mysql.statement.ddl.MySQLAlterTableStatement;
import com.dc.parser.ext.mysql.utils.MySQLCreateTableHelper;
import com.dc.parser.model.enums.CheckRuleDatabasePrefix;
import com.dc.parser.model.enums.CheckRuleUniqueKey;
import com.dc.parser.model.check.rule.CheckResult;
import com.dc.parser.model.check.rule.CheckRuleParameter;
import com.dc.parser.model.check.rule.SQLRule;
import com.dc.parser.model.segment.ddl.column.ColumnDefinitionSegment;
import com.dc.parser.model.statement.SQLStatement;
import com.dc.parser.model.statement.ddl.CreateTableStatement;

import java.util.*;
import java.util.stream.Stream;

import static com.dc.parser.ext.mysql.utils.MySQLAlterTableHelper.getColumnDefinitionsFromAlterTableStatement;

public class MySQLCheckDefaultNullRule implements SQLRule {

    private final Set<String> NO_SUGGEST_TYPE = Set.of(
            "TINYBLOB","BLOB","MEDIUMBLOB","LONGBLOB","TINYTEXT","TEXT","MEDIUMTEXT","LONGTEXT"
    );

    @Override
    public String getType() {
        return CheckRuleDatabasePrefix.MYSQL_.getValue() + CheckRuleUniqueKey.DDL_CHECK_BLOB_TEXT_DEFAULT_NULL.getValue();
    }

    @Override
    public CheckResult check(SQLStatement sqlStatement, CheckRuleParameter parameter) {

        boolean allNullDefault = true;

        if (sqlStatement instanceof CreateTableStatement) {
            CreateTableStatement createTableStatement = (CreateTableStatement) sqlStatement;

            Stream<MySQLColumnDefinitionSegment> columnDefinitionsFromAlterTableStatement = MySQLCreateTableHelper.getColumnDefinitionsFromAlterTableStatement(createTableStatement);
            allNullDefault = verifyAllNullDefault(columnDefinitionsFromAlterTableStatement);

        } else if (sqlStatement instanceof MySQLAlterTableStatement) {
            MySQLAlterTableStatement alterTableStatement = (MySQLAlterTableStatement) sqlStatement;

            Stream<MySQLColumnDefinitionSegment> columnDefinitionsFromAlterTableStatement = getColumnDefinitionsFromAlterTableStatement(alterTableStatement);
            allNullDefault = verifyAllNullDefault(columnDefinitionsFromAlterTableStatement);
        }

        if (!allNullDefault) {
            return CheckResult.buildFailResult(parameter.getCheckRuleContent());
        }

        return CheckResult.DEFAULT_SUCCESS_RESULT;
    }

    public boolean verifyAllNullDefault(Stream<? extends ColumnDefinitionSegment> columnDefinitionSegmentStream) {
        return columnDefinitionSegmentStream.filter(columnDefinitionSegment -> NO_SUGGEST_TYPE.contains(columnDefinitionSegment.getDataType().getDataTypeName().toUpperCase()))
                .allMatch(columnDefinitionSegment ->
                        columnDefinitionSegment.getDefaultSegment()
                                .map(defaultSegment -> "NULL".equalsIgnoreCase(defaultSegment.getDefaultName()))
                                .orElse(true)
                );
    }
}
