package com.dc.parser.sensitive.model;

public class SubQueryFromSourceTab {


    private String column;
    private String table;
    private String alias;

    public SubQueryFromSourceTab() {
    }

    public SubQueryFromSourceTab(String column, String table, String alias) {
        this.column = column;
        this.table = table;
        this.alias = alias;
    }

    public String getColumn() {
        return column;
    }

    public void setColumn(String column) {
        this.column = column;
    }

    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }
}
